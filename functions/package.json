{"name": "money-mouthy-functions", "description": "Firebase Functions for Money Mouthy App - Stripe Payment Integration", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "main": "lib/index.js", "dependencies": {"firebase-admin": "^12.0.0", "firebase-functions": "^4.8.0", "stripe": "^14.15.0", "cors": "^2.8.5"}, "devDependencies": {"typescript": "^4.9.0", "@types/cors": "^2.8.17"}, "private": true}