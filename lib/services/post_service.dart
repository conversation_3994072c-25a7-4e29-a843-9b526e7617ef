// ignore_for_file: avoid_print

import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import '../utils/debouncer.dart';

class Post {
  final String id;
  final String author;
  final String authorId;
  final String? title;
  final String content;
  final double price;
  final String category;
  final List<String> tags;
  final DateTime createdAt;
  final int likes;
  final int views;
  final bool isPaid;
  final bool isPublic;
  final bool allowComments;
  final String? imageUrl; // Keep for backward compatibility
  final List<String> imageUrls; // Multiple images
  final List<String> videoUrls; // Multiple videos
  final String? linkUrl;
  final List<String> likedBy; // Users who liked this post

  Post({
    required this.id,
    required this.author,
    required this.authorId,
    this.title,
    required this.content,
    required this.price,
    required this.category,
    this.tags = const [],
    required this.createdAt,
    this.likes = 0,
    this.views = 0,
    this.isPaid = false,
    this.isPublic = true,
    this.allowComments = true,
    this.imageUrl,
    this.imageUrls = const [],
    this.videoUrls = const [],
    this.linkUrl,
    this.likedBy = const [],
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'author': author,
      'authorId': authorId,
      'title': title,
      'content': content,
      'price': price,
      'category': category,
      'tags': tags,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'likes': likes,
      'views': views,
      'isPaid': isPaid,
      'isPublic': isPublic,
      'allowComments': allowComments,
      'imageUrl': imageUrl,
      'imageUrls': imageUrls,
      'videoUrls': videoUrls,
      'linkUrl': linkUrl,
      'likedBy': likedBy,
    };
  }

  static Post fromMap(Map<String, dynamic> map) {
    return Post(
      id: map['id'],
      author: map['author'],
      authorId: map['authorId'],
      title: map['title'],
      content: map['content'],
      price: map['price'].toDouble(),
      category: map['category'],
      tags: List<String>.from(map['tags'] ?? []),
      createdAt:
          map['createdAt'] is int
              ? DateTime.fromMillisecondsSinceEpoch(map['createdAt'])
              : (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      likes: map['likes'] ?? 0,
      views: map['views'] ?? 0,
      isPaid: map['isPaid'] ?? false,
      isPublic: map['isPublic'] ?? true,
      allowComments: map['allowComments'] ?? true,
      imageUrl: map['imageUrl'],
      imageUrls: List<String>.from(map['imageUrls'] ?? []),
      videoUrls: List<String>.from(map['videoUrls'] ?? []),
      linkUrl: map['linkUrl'],
      likedBy: List<String>.from(map['likedBy'] ?? []),
    );
  }

  Post copyWith({
    String? id,
    String? author,
    String? authorId,
    String? title,
    String? content,
    double? price,
    String? category,
    List<String>? tags,
    DateTime? createdAt,
    int? likes,
    int? views,
    bool? isPaid,
    bool? isPublic,
    bool? allowComments,
    String? imageUrl,
    List<String>? imageUrls,
    List<String>? videoUrls,
    String? linkUrl,
    List<String>? likedBy,
  }) {
    return Post(
      id: id ?? this.id,
      author: author ?? this.author,
      authorId: authorId ?? this.authorId,
      title: title ?? this.title,
      content: content ?? this.content,
      price: price ?? this.price,
      category: category ?? this.category,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
      likes: likes ?? this.likes,
      views: views ?? this.views,
      isPaid: isPaid ?? this.isPaid,
      isPublic: isPublic ?? this.isPublic,
      allowComments: allowComments ?? this.allowComments,
      imageUrl: imageUrl ?? this.imageUrl,
      imageUrls: imageUrls ?? this.imageUrls,
      videoUrls: videoUrls ?? this.videoUrls,
      linkUrl: linkUrl ?? this.linkUrl,
      likedBy: likedBy ?? this.likedBy,
    );
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  String get formattedPrice {
    if (price == 0) return 'Free';
    return '\$${price.toStringAsFixed(2)}';
  }
}

class PostService {
  static final PostService _instance = PostService._internal();
  factory PostService() => _instance;
  PostService._internal() {
    _postsController = StreamController<List<Post>>.broadcast();
  }

  final List<Post> _posts = [];
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? _postsSubscription;
  late final StreamController<List<Post>> _postsController;

  bool _isInitialized = false;
  bool _isInitializing = false;
  Completer<void>? _initializationCompleter;

  // Pagination support
  static const int _postsPerPage = 20;
  DocumentSnapshot? _lastDocument;
  bool _hasMorePosts = true;
  bool _isLoadingMore = false;

  // Debouncing for user interactions
  final Map<String, Debouncer> _debouncers = {};
  final RateLimiter _likeRateLimiter = RateLimiter(
    maxRequests: 10,
    timeWindow: const Duration(minutes: 1),
  );

  String get _currentUserId => _auth.currentUser?.uid ?? 'anonymous';
  String get _currentUserName =>
      _auth.currentUser?.displayName ??
      _auth.currentUser?.email ??
      'Anonymous User';

  // Stream getter for real-time posts
  Stream<List<Post>> get postsStream {
    return Stream.multi((controller) {
      // Emit current posts immediately if available
      if (_posts.isNotEmpty) {
        // print(
        //   'PostService: Emitting ${_posts.length} current posts to new listener',
        // );
        controller.add(List.unmodifiable(_posts));
      }

      // Listen to future updates
      final subscription = _postsController.stream.listen(
        (posts) {
          controller.add(posts);
        },
        onError: controller.addError,
        onDone: controller.close,
      );

      controller.onCancel = () {
        subscription.cancel();
      };
    });
  }

  // Getter to check if service is initialized
  bool get isInitialized => _isInitialized;

  // Future that completes when service is initialized
  Future<void> get initialized =>
      _initializationCompleter?.future ?? Future.value();

  // Get filtered and sorted posts as a stream
  Stream<List<Post>> getPostsStreamSortedBy(String sortBy, {String? category}) {
    return postsStream.map((posts) {
      var filteredPosts =
          category == null || category == 'All'
              ? List<Post>.from(posts)
              : posts.where((post) => post.category == category).toList();

      switch (sortBy) {
        case 'Highest Paid':
          filteredPosts.sort((a, b) {
            final priceComparison = b.price.compareTo(a.price);
            if (priceComparison != 0) return priceComparison;
            // Same price, sort by creation date (latest first)
            return b.createdAt.compareTo(a.createdAt);
          });
          break;
        case 'Most Popular':
          filteredPosts.sort((a, b) {
            final likesComparison = b.likes.compareTo(a.likes);
            if (likesComparison != 0) return likesComparison;
            // Same likes, sort by creation date (latest first)
            return b.createdAt.compareTo(a.createdAt);
          });
          break;
        case 'Recent':
          filteredPosts.sort((a, b) => b.createdAt.compareTo(a.createdAt));
          break;
        case 'Most Views':
          filteredPosts.sort((a, b) {
            final viewsComparison = b.views.compareTo(a.views);
            if (viewsComparison != 0) return viewsComparison;
            // Same views, sort by creation date (latest first)
            return b.createdAt.compareTo(a.createdAt);
          });
          break;
      }

      return filteredPosts;
    });
  }

  Future<void> initialize() async {
    if (_isInitialized) {
      return;
    }

    if (_isInitializing) {
      return _initializationCompleter?.future ?? Future.value();
    }

    _isInitializing = true;
    _initializationCompleter = Completer<void>();

    try {
      _listenToPosts();

      // Start automatic cleanup of old posts
      scheduleAutomaticCleanup();

      _isInitialized = true;
      _initializationCompleter?.complete();
    } catch (e) {
      _initializationCompleter?.completeError(e);
      rethrow;
    } finally {
      _isInitializing = false;
    }
  }

  void _listenToPosts() {
    _postsSubscription?.cancel();

    // Load initial batch with pagination
    _loadInitialPosts();
  }

  /// Load initial batch of posts with pagination and offline support
  Future<void> _loadInitialPosts() async {
    try {
      final query = _firestore
          .collection('posts')
          .orderBy('createdAt', descending: true)
          .limit(_postsPerPage);

      // Try to get from cache first for better performance
      final snapshot = await query.get(const GetOptions(source: Source.cache));

      if (snapshot.docs.isNotEmpty) {
        _updatePostsFromSnapshot(snapshot);
        debugPrint(
          'PostService: Loaded ${_posts.length} initial posts from cache',
        );
      }

      // Then get from server to ensure fresh data
      try {
        final serverSnapshot = await query.get(
          const GetOptions(source: Source.server),
        );
        _updatePostsFromSnapshot(serverSnapshot);
        debugPrint('PostService: Updated ${_posts.length} posts from server');
      } catch (e) {
        // If server fetch fails, we still have cache data
        debugPrint('PostService: Server fetch failed, using cache: $e');
      }
    } catch (e) {
      debugPrint('PostService: Error loading initial posts: $e');
      _postsController.addError(e);
    }
  }

  /// Helper method to update posts from snapshot
  void _updatePostsFromSnapshot(QuerySnapshot<Map<String, dynamic>> snapshot) {
    _posts.clear();
    _posts.addAll(
      snapshot.docs.map((doc) {
        final data = doc.data();
        final map = Map<String, dynamic>.from(data)..['id'] = doc.id;
        return Post.fromMap(map);
      }),
    );

    // Update pagination state
    if (snapshot.docs.isNotEmpty) {
      _lastDocument = snapshot.docs.last;
      _hasMorePosts = snapshot.docs.length == _postsPerPage;
    } else {
      _hasMorePosts = false;
    }

    // Emit updated posts to stream
    _postsController.add(List.unmodifiable(_posts));
  }

  Future<String> createPost({
    String? title,
    required String content,
    required double price,
    required String category,
    List<String>? tags,
    bool isPublic = true,
    bool allowComments = true,
    String? imageUrl, // Keep for backward compatibility
    List<String>? imageUrls, // New field for multiple images
    List<String>? videoUrls, // New field for videos
    String? linkUrl,
  }) async {
    final docRef = await _firestore.collection('posts').add({
      'author': _currentUserName,
      'authorId': _currentUserId,
      'title': title?.trim().isEmpty == true ? null : title?.trim(),
      'content': content.trim(),
      'price': price,
      'category': category,
      'tags': tags ?? [],
      'createdAt': FieldValue.serverTimestamp(),
      'likes': 0,
      'comments': 0,
      'views': 0,
      'isPaid': false,
      'isPublic': isPublic,
      'allowComments': allowComments,
      'imageUrl': imageUrl, // Keep for backward compatibility
      'imageUrls': imageUrls ?? [], // Multiple images
      'videoUrls': videoUrls ?? [], // Multiple videos
      'linkUrl': linkUrl,
    });

    return docRef.id;
  }

  List<Post> getAllPosts() {
    return List.unmodifiable(_posts);
  }

  /// Get all posts, ensuring service is initialized first
  Future<List<Post>> getAllPostsAsync() async {
    if (!_isInitialized) {
      await initialized;
    }
    return List.unmodifiable(_posts);
  }

  /// Force refresh posts from Firestore
  Future<void> refreshPosts() async {
    try {
      // Reset pagination state
      _lastDocument = null;
      _hasMorePosts = true;
      _isLoadingMore = false;

      // Cancel existing subscription and restart
      _postsSubscription?.cancel();
      _listenToPosts();
    } catch (e) {
      rethrow;
    }
  }

  /// Load more posts for pagination
  Future<bool> loadMorePosts() async {
    if (_isLoadingMore || !_hasMorePosts || _lastDocument == null) {
      return false;
    }

    _isLoadingMore = true;

    try {
      final query = _firestore
          .collection('posts')
          .orderBy('createdAt', descending: true)
          .startAfterDocument(_lastDocument!)
          .limit(_postsPerPage);

      final snapshot = await query.get();

      if (snapshot.docs.isNotEmpty) {
        final newPosts =
            snapshot.docs.map((doc) {
              final data = doc.data();
              final map = Map<String, dynamic>.from(data)..['id'] = doc.id;
              return Post.fromMap(map);
            }).toList();

        _posts.addAll(newPosts);
        _lastDocument = snapshot.docs.last;
        _hasMorePosts = snapshot.docs.length == _postsPerPage;

        // Emit updated posts to stream
        _postsController.add(List.unmodifiable(_posts));

        debugPrint(
          'PostService: Loaded ${newPosts.length} more posts. Total: ${_posts.length}',
        );
        return true;
      } else {
        _hasMorePosts = false;
        return false;
      }
    } catch (e) {
      debugPrint('PostService: Error loading more posts: $e');
      return false;
    } finally {
      _isLoadingMore = false;
    }
  }

  /// Check if more posts can be loaded
  bool get canLoadMore => _hasMorePosts && !_isLoadingMore;

  /// Check if currently loading more posts
  bool get isLoadingMore => _isLoadingMore;

  List<Post> getPostsByCategory(String category) {
    if (category == 'All') return getAllPosts();
    return _posts.where((post) => post.category == category).toList();
  }

  List<Post> getPostsSortedBy(String sortBy, {String? category}) {
    var posts =
        category == null || category == 'All'
            ? List<Post>.from(_posts)
            : getPostsByCategory(category);

    switch (sortBy) {
      case 'Highest Paid':
        posts.sort((a, b) {
          final priceComparison = b.price.compareTo(a.price);
          if (priceComparison != 0) return priceComparison;
          // Same price, sort by creation date (latest first)
          return b.createdAt.compareTo(a.createdAt);
        });
        break;
      case 'Most Popular':
        posts.sort((a, b) {
          final likesComparison = b.likes.compareTo(a.likes);
          if (likesComparison != 0) return likesComparison;
          // Same likes, sort by creation date (latest first)
          return b.createdAt.compareTo(a.createdAt);
        });
        break;
      case 'Recent':
        posts.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case 'Most Views':
        posts.sort((a, b) {
          final viewsComparison = b.views.compareTo(a.views);
          if (viewsComparison != 0) return viewsComparison;
          // Same views, sort by creation date (latest first)
          return b.createdAt.compareTo(a.createdAt);
        });
        break;
    }

    return posts;
  }

  // Get top paid post for a specific category (24-hour system)
  Post? getTopPaidPostForCategory(String category) {
    final categoryPosts = getPostsByCategory(category);
    // print(
    //   'PostService: Category "$category" has ${categoryPosts.length} posts',
    // );

    if (categoryPosts.isEmpty) return null;

    // Filter posts with price > 0 first
    final paidPosts = categoryPosts.where((post) => post.price > 0).toList();
    // print(
    //   'PostService: Found ${paidPosts.length} paid posts in category "$category"',
    // );

    // For debugging: show all posts in this category with their prices
    for (final post in categoryPosts.take(5)) {
      final contentPreview =
          post.content.length > 30
              ? post.content.substring(0, 30)
              : post.content;
    }

    // If no paid posts, fall back to highest priced post (even if price is 0) for testing
    if (paidPosts.isEmpty) {
      // print(
      //   'PostService: No paid posts found, falling back to any post for testing',
      // );
      if (categoryPosts.isNotEmpty) {
        // Sort all posts by price (highest first) and return the top one
        categoryPosts.sort((a, b) {
          final priceComparison = b.price.compareTo(a.price);
          if (priceComparison != 0) return priceComparison;
          return b.createdAt.compareTo(a.createdAt);
        });
        return categoryPosts.first;
      }
      return null;
    }

    // Filter posts from last 24 hours
    final now = DateTime.now();
    final last24Hours =
        paidPosts.where((post) {
          final hoursDiff = now.difference(post.createdAt).inHours;
          return hoursDiff <= 24;
        }).toList();

    // print(
    //   'PostService: Found ${last24Hours.length} paid posts in last 24 hours for category "$category"',
    // );

    // If no posts in last 24 hours, fall back to all paid posts
    final postsToSort = last24Hours.isNotEmpty ? last24Hours : paidPosts;

    // Sort by highest price first, then by latest creation date when prices are equal
    postsToSort.sort((a, b) {
      final priceComparison = b.price.compareTo(a.price);
      if (priceComparison != 0) {
        return priceComparison; // Different prices, sort by price
      }
      // Same price, sort by creation date (latest first)
      return b.createdAt.compareTo(a.createdAt);
    });

    final topPost = postsToSort.first;
    final topPostPreview =
        topPost.content.length > 30
            ? topPost.content.substring(0, 30)
            : topPost.content;
    // print(
    //   'PostService: Top paid post for "$category": "$topPostPreview..." with price \$${topPost.price}',
    // );

    return topPost;
  }

  // Get all top paid posts for each category
  Map<String, Post?> getTopPaidPostsForAllCategories() {
    final categories = [
      'Politics',
      'News',
      'Sports',
      'Entertainment',
      'Sex',
      'Religion',
    ];
    final Map<String, Post?> topPosts = {};

    for (final category in categories) {
      topPosts[category] = getTopPaidPostForCategory(category);
    }

    return topPosts;
  }

  // Delete posts older than 48 hours (storage optimization)
  Future<void> deleteOldPosts() async {
    final now = DateTime.now();
    final postsToDelete = <String>[];

    for (final post in _posts) {
      final hoursDiff = now.difference(post.createdAt).inHours;
      if (hoursDiff >= 48) {
        postsToDelete.add(post.id);
      }
    }

    // Delete from Firestore
    final batch = _firestore.batch();
    for (final postId in postsToDelete) {
      batch.delete(_firestore.collection('posts').doc(postId));
    }

    if (postsToDelete.isNotEmpty) {
      await batch.commit();
    }
  }

  // Schedule automatic cleanup (call this periodically)
  void scheduleAutomaticCleanup() {
    Timer.periodic(const Duration(hours: 1), (timer) {
      deleteOldPosts();
    });
  }

  Post? getPostById(String id) {
    try {
      return _posts.firstWhere((post) => post.id == id);
    } catch (e) {
      return null;
    }
  }

  Future<void> likePost(String postId) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return;

    // Rate limiting to prevent spam
    if (!_likeRateLimiter.tryRequest()) {
      debugPrint('PostService: Like rate limit exceeded');
      return;
    }

    // Debounce like actions to prevent rapid tapping
    final debounceKey = 'like_$postId';
    _debouncers[debounceKey]?.cancel();
    _debouncers[debounceKey] = Debouncer(
      delay: const Duration(milliseconds: 300),
    );

    _debouncers[debounceKey]!.call(() async {
      try {
        final postRef = _firestore.collection('posts').doc(postId);

        await _firestore.runTransaction((transaction) async {
          final postDoc = await transaction.get(postRef);
          if (!postDoc.exists) return;

          final data = postDoc.data()!;
          final likedBy = List<String>.from(data['likedBy'] ?? []);
          final currentLikes = data['likes'] ?? 0;

          if (likedBy.contains(currentUser.uid)) {
            // Unlike
            likedBy.remove(currentUser.uid);
            transaction.update(postRef, {
              'likedBy': likedBy,
              'likes': currentLikes - 1,
            });
          } else {
            // Like
            likedBy.add(currentUser.uid);
            transaction.update(postRef, {
              'likedBy': likedBy,
              'likes': currentLikes + 1,
            });
          }
        });
      } catch (e) {
        debugPrint('PostService: Error liking post: $e');
      }
    });
  }

  Future<void> viewPost(String postId) async {
    await _firestore.collection('posts').doc(postId).update({
      'views': FieldValue.increment(1),
    });
  }

  Future<void> deletePost(String postId) async {
    final doc = _firestore.collection('posts').doc(postId);
    final snapshot = await doc.get();
    if (snapshot.exists && snapshot['authorId'] == _currentUserId) {
      await doc.delete();
    }
  }

  List<Post> getUserPosts() {
    return _posts.where((post) => post.authorId == _currentUserId).toList();
  }

  List<String> getCategories() {
    return [
      'All',
      'Politics',
      'News',
      'Sports',
      'Sex',
      'Entertainment',
      'Religion',
    ];
  }

  List<String> getSortOptions() {
    return ['Highest Paid', 'Most Popular', 'Recent', 'Most Views'];
  }

  // Statistics
  int get totalPosts => _posts.length;
  int get userPostsCount => getUserPosts().length;

  double get averagePostPrice {
    if (_posts.isEmpty) return 0.0;
    return _posts.map((p) => p.price).reduce((a, b) => a + b) / _posts.length;
  }

  double get totalValueInPosts {
    return _posts.map((p) => p.price).fold(0.0, (a, b) => a + b);
  }

  Map<String, int> get postsByCategory {
    final Map<String, int> categoryCount = {};
    for (final post in _posts) {
      categoryCount[post.category] = (categoryCount[post.category] ?? 0) + 1;
    }
    return categoryCount;
  }

  void dispose() {
    _postsSubscription?.cancel();
    _postsController.close();
  }
}
