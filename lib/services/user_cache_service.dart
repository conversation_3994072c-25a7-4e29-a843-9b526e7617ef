import 'dart:async';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// Centralized user data caching service to eliminate N+1 queries
/// Uses GetX for reactive state management and smart caching
class UserCacheService extends GetxController {
  static UserCacheService get instance => Get.find();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  // Cache with TTL (Time To Live)
  final _userCache = <String, Map<String, dynamic>>{}.obs;
  final _cacheTimestamps = <String, DateTime>{};
  final _pendingRequests = <String, Completer<Map<String, dynamic>?>>{};
  
  // Cache configuration
  static const Duration _cacheTTL = Duration(minutes: 5);
  static const int _maxCacheSize = 100;
  
  // Reactive getters
  Map<String, dynamic> getUserFromCache(String userId) {
    return _userCache[userId] ?? {};
  }
  
  bool isUserCached(String userId) {
    if (!_userCache.containsKey(userId)) return false;
    
    final timestamp = _cacheTimestamps[userId];
    if (timestamp == null) return false;
    
    return DateTime.now().difference(timestamp) < _cacheTTL;
  }

  /// Get user data with smart caching
  Future<Map<String, dynamic>?> getUserData(String userId) async {
    if (userId.isEmpty) return null;

    // Check cache first
    if (isUserCached(userId)) {
      debugPrint('UserCacheService: Cache hit for user $userId');
      return _userCache[userId];
    }

    // Check if request is already pending
    if (_pendingRequests.containsKey(userId)) {
      debugPrint('UserCacheService: Request already pending for user $userId');
      return await _pendingRequests[userId]!.future;
    }

    // Create new request
    final completer = Completer<Map<String, dynamic>?>();
    _pendingRequests[userId] = completer;

    try {
      debugPrint('UserCacheService: Fetching user data for $userId');
      
      final userDoc = await _firestore
          .collection('users')
          .doc(userId)
          .get()
          .timeout(const Duration(seconds: 10));

      if (userDoc.exists) {
        final userData = userDoc.data()!;
        
        // Update cache
        _updateCache(userId, userData);
        
        completer.complete(userData);
        return userData;
      } else {
        completer.complete(null);
        return null;
      }
    } catch (e) {
      debugPrint('UserCacheService: Error fetching user $userId: $e');
      completer.completeError(e);
      return null;
    } finally {
      _pendingRequests.remove(userId);
    }
  }

  /// Batch load multiple users efficiently
  Future<Map<String, Map<String, dynamic>>> batchGetUsers(List<String> userIds) async {
    final result = <String, Map<String, dynamic>>{};
    final uncachedIds = <String>[];

    // Check cache for each user
    for (final userId in userIds) {
      if (isUserCached(userId)) {
        result[userId] = _userCache[userId]!;
      } else {
        uncachedIds.add(userId);
      }
    }

    // Batch fetch uncached users
    if (uncachedIds.isNotEmpty) {
      try {
        // Firebase doesn't support batch get with different document IDs efficiently
        // So we'll use concurrent individual requests with a limit
        const batchSize = 10;
        
        for (int i = 0; i < uncachedIds.length; i += batchSize) {
          final batch = uncachedIds.skip(i).take(batchSize).toList();
          
          final futures = batch.map((userId) => getUserData(userId));
          final results = await Future.wait(futures);
          
          for (int j = 0; j < batch.length; j++) {
            if (results[j] != null) {
              result[batch[j]] = results[j]!;
            }
          }
        }
      } catch (e) {
        debugPrint('UserCacheService: Error in batch fetch: $e');
      }
    }

    return result;
  }

  /// Update cache with new user data
  void _updateCache(String userId, Map<String, dynamic> userData) {
    // Manage cache size
    if (_userCache.length >= _maxCacheSize) {
      _evictOldestEntries();
    }

    _userCache[userId] = userData;
    _cacheTimestamps[userId] = DateTime.now();
    
    debugPrint('UserCacheService: Cached user data for $userId');
  }

  /// Evict oldest cache entries
  void _evictOldestEntries() {
    final sortedEntries = _cacheTimestamps.entries.toList()
      ..sort((a, b) => a.value.compareTo(b.value));
    
    // Remove oldest 20% of entries
    final removeCount = (_maxCacheSize * 0.2).ceil();
    
    for (int i = 0; i < removeCount && i < sortedEntries.length; i++) {
      final userId = sortedEntries[i].key;
      _userCache.remove(userId);
      _cacheTimestamps.remove(userId);
    }
    
    debugPrint('UserCacheService: Evicted $removeCount old cache entries');
  }

  /// Invalidate cache for specific user
  void invalidateUser(String userId) {
    _userCache.remove(userId);
    _cacheTimestamps.remove(userId);
    debugPrint('UserCacheService: Invalidated cache for user $userId');
  }

  /// Clear all cache
  void clearCache() {
    _userCache.clear();
    _cacheTimestamps.clear();
    debugPrint('UserCacheService: Cleared all cache');
  }

  /// Preload users for better performance
  Future<void> preloadUsers(List<String> userIds) async {
    final uncachedIds = userIds.where((id) => !isUserCached(id)).toList();
    
    if (uncachedIds.isNotEmpty) {
      debugPrint('UserCacheService: Preloading ${uncachedIds.length} users');
      await batchGetUsers(uncachedIds);
    }
  }

  @override
  void onClose() {
    clearCache();
    super.onClose();
  }
}
