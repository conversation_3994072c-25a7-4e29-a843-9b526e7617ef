import 'dart:async';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';

import '../models/transaction_model.dart';
import '../repositories/payment_repository.dart';
import '../services/transaction_manager.dart';

enum WalletStatus { uninitialized, initializing, ready, error, syncing }

class WalletController extends GetxController {
  static WalletController get to => Get.find();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final PaymentRepository _paymentRepository = PaymentRepository();
  final TransactionManager _transactionManager = TransactionManager();

  // Reactive variables
  final _balance = 0.0.obs;
  final _transactions = <TransactionModel>[].obs;
  final _status = WalletStatus.uninitialized.obs;
  final _errorMessage = RxnString();
  final _isLoading = false.obs;
  final _totalEarnings = 0.0.obs;
  final _totalSpent = 0.0.obs;
  final _lastUpdated = DateTime.now().obs;

  // Stream subscriptions
  StreamSubscription<DocumentSnapshot>? _balanceSubscription;
  StreamSubscription<QuerySnapshot>? _transactionsSubscription;
  DocumentReference<Map<String, dynamic>>? _walletDoc;

  bool _isInitialized = false;
  Completer<void>? _initializationCompleter;

  // Getters
  double get balance => _balance.value;
  List<TransactionModel> get transactions => _transactions.toList();
  WalletStatus get status => _status.value;
  String? get errorMessage => _errorMessage.value;
  bool get isLoading => _isLoading.value;
  double get totalEarnings => _totalEarnings.value;
  double get totalSpent => _totalSpent.value;
  DateTime get lastUpdated => _lastUpdated.value;
  bool get isInitialized => _isInitialized;
  bool get isReady => _status.value == WalletStatus.ready;
  bool get hasError => _status.value == WalletStatus.error;

  // Computed properties
  List<TransactionModel> get creditTransactions =>
      _transactions.where((t) => t.isCredit).toList();

  List<TransactionModel> get debitTransactions =>
      _transactions.where((t) => t.isDebit).toList();

  List<TransactionModel> get pendingTransactions =>
      _transactions.where((t) => t.isPending).toList();

  @override
  void onInit() {
    super.onInit();
    // Auto-initialize when controller is created
    ever(_status, (WalletStatus status) {
      debugPrint('Wallet status changed to: $status');
    });
  }

  @override
  void onClose() {
    _balanceSubscription?.cancel();
    _transactionsSubscription?.cancel();
    super.onClose();
  }

  Future<void> initialize() async {
    if (_isInitialized) return;

    if (_initializationCompleter != null) {
      return _initializationCompleter!.future;
    }

    _initializationCompleter = Completer<void>();

    try {
      _updateStatus(WalletStatus.initializing);
      _isLoading.value = true;

      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Initialize payment repository with Stripe (production-ready)
      await _paymentRepository.initialize(provider: PaymentProvider.stripe);
      await _transactionManager.initialize(user.uid);

      _walletDoc = _firestore.collection('wallets').doc(user.uid);

      // Initialize wallet document if it doesn't exist
      await _initializeWalletDocument();

      // Set up real-time listeners
      await _setupListeners();

      _isInitialized = true;
      _updateStatus(WalletStatus.ready);
      _isLoading.value = false;

      _initializationCompleter!.complete();
    } catch (e) {
      _updateStatus(WalletStatus.error);
      _errorMessage.value = 'Failed to initialize wallet: $e';
      _isLoading.value = false;
      _initializationCompleter!.completeError(e);
      rethrow;
    }
  }

  Future<void> _initializeWalletDocument() async {
    if (_walletDoc == null) return;

    final docSnapshot = await _walletDoc!.get();
    if (!docSnapshot.exists) {
      // Create new wallet document
      await _walletDoc!.set({
        'balance': 0.0,
        'createdAt': FieldValue.serverTimestamp(),
        'lastUpdated': FieldValue.serverTimestamp(),
      });
    } else {
      // Ensure balance field exists
      final data = docSnapshot.data();
      if (data == null || !data.containsKey('balance')) {
        await _walletDoc!.update({
          'balance': 0.0,
          'lastUpdated': FieldValue.serverTimestamp(),
        });
      }
    }
  }

  Future<void> _setupListeners() async {
    if (_walletDoc == null) return;

    // Listen to balance changes
    _balanceSubscription = _walletDoc!.snapshots().listen(
      (snapshot) {
        if (snapshot.exists) {
          final data = snapshot.data();
          final balance = (data?['balance'] ?? 0.0).toDouble();
          _balance.value = balance;
          _lastUpdated.value = DateTime.now();
        }
      },
      onError: (error) {
        debugPrint('Error listening to wallet balance: $error');
        _updateStatus(WalletStatus.error);
        _errorMessage.value = 'Failed to sync wallet balance: $error';
      },
    );

    // Listen to transaction changes
    _transactionsSubscription = _walletDoc!
        .collection('transactions')
        .orderBy('timestamp', descending: true)
        .limit(100)
        .snapshots()
        .listen(
          (snapshot) {
            final transactions =
                snapshot.docs
                    .map((doc) => TransactionModel.fromFirestore(doc))
                    .toList();

            _transactions.value = transactions;
            _calculateTotals(transactions);
            _lastUpdated.value = DateTime.now();
          },
          onError: (error) {
            debugPrint('Error listening to transactions: $error');
          },
        );
  }

  void _calculateTotals(List<TransactionModel> transactions) {
    final earnings = transactions
        .where((t) => t.isCredit && t.isCompleted)
        .fold(0.0, (total, t) => total + t.amount);

    final spent = transactions
        .where((t) => t.isDebit && t.isCompleted)
        .fold(0.0, (total, t) => total + t.amount);

    _totalEarnings.value = earnings;
    _totalSpent.value = spent;
  }

  void _updateStatus(WalletStatus newStatus) {
    _status.value = newStatus;
  }

  Future<bool> addFunds(double amount, {String? paymentMethodId}) async {
    if (!_isInitialized) {
      throw StateError('WalletController not initialized');
    }

    // Production-ready validation
    if (amount <= 0) {
      throw ArgumentError('Amount must be positive');
    }

    if (amount < 0.50) {
      throw ArgumentError('Minimum amount is \$0.50');
    }

    if (amount > 999999.00) {
      throw ArgumentError('Maximum amount is \$999,999.00');
    }

    _isLoading.value = true;

    try {
      // Process payment with production metadata
      final paymentResult = await _paymentRepository.processPayment(
        amount: amount,
        currency: 'USD',
        paymentMethodId: paymentMethodId,
        metadata: {
          'userId': FirebaseAuth.instance.currentUser?.uid ?? 'unknown',
          'type': 'wallet_funding',
          'timestamp': DateTime.now().toIso8601String(),
          'app_version': '1.0.0',
          'platform': kIsWeb ? 'web' : 'mobile',
        },
      );

      if (!paymentResult.isSuccess) {
        _isLoading.value = false;
        return false;
      }

      // Create transaction record with production details
      final transaction = TransactionModel(
        id: '', // Will be set by Firestore
        type: TransactionType.credit,
        amount: amount,
        description: 'Wallet funding via Stripe - ${formatCurrency(amount)}',
        timestamp: DateTime.now(),
        status: TransactionStatus.completed,
        paymentMethodId: paymentMethodId,
        externalTransactionId: paymentResult.transactionId,
      );

      // Add transaction and update balance atomically
      await _transactionManager.addTransaction(
        transaction,
        _balance.value + amount,
      );

      _isLoading.value = false;
      return true;
    } catch (e) {
      debugPrint('Error adding funds: $e');
      _updateStatus(WalletStatus.error);

      // Production-ready error messages
      String userFriendlyMessage;
      if (e.toString().contains('network') ||
          e.toString().contains('connection')) {
        userFriendlyMessage =
            'Network error. Please check your connection and try again.';
      } else if (e.toString().contains('card') ||
          e.toString().contains('payment')) {
        userFriendlyMessage =
            'Payment failed. Please check your payment method.';
      } else if (e.toString().contains('amount')) {
        userFriendlyMessage = e.toString();
      } else {
        userFriendlyMessage =
            'Unable to process payment. Please try again or contact support.';
      }

      _errorMessage.value = userFriendlyMessage;
      _isLoading.value = false;
      return false;
    }
  }

  Future<bool> deductFunds({
    required double amount,
    required String description,
    String? postId,
  }) async {
    if (!_isInitialized) {
      throw StateError('WalletController not initialized');
    }

    if (amount <= 0) {
      throw ArgumentError('Amount must be positive');
    }

    if (_balance.value < amount) {
      return false; // Insufficient funds
    }

    _isLoading.value = true;

    try {
      final transaction = TransactionModel(
        id: '', // Will be set by Firestore
        type: TransactionType.debit,
        amount: amount,
        description: description,
        timestamp: DateTime.now(),
        status: TransactionStatus.completed,
        postId: postId,
      );

      // Deduct from balance and add transaction atomically
      await _transactionManager.addTransaction(
        transaction,
        _balance.value - amount,
      );

      _isLoading.value = false;
      return true;
    } catch (e) {
      _updateStatus(WalletStatus.error);
      _errorMessage.value = 'Failed to deduct funds: $e';
      _isLoading.value = false;
      return false;
    }
  }

  String formatCurrency(double amount) {
    return NumberFormat.currency(symbol: '\$', decimalDigits: 2).format(amount);
  }

  void clearError() {
    _updateStatus(WalletStatus.ready);
    _errorMessage.value = null;
  }

  // Refresh wallet data manually
  @override
  Future<void> refresh() async {
    if (_isInitialized) {
      await initialize();
    }
  }
}
