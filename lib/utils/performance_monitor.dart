import 'dart:async';
import 'package:flutter/foundation.dart';

/// Performance monitoring utility to track app performance improvements
class PerformanceMonitor {
  static final PerformanceMonitor _instance = PerformanceMonitor._internal();
  factory PerformanceMonitor() => _instance;
  PerformanceMonitor._internal();

  final Map<String, DateTime> _startTimes = {};
  final Map<String, List<Duration>> _measurements = {};
  final Map<String, int> _counters = {};

  /// Start timing an operation
  void startTimer(String operation) {
    _startTimes[operation] = DateTime.now();
  }

  /// End timing an operation and record the duration
  Duration? endTimer(String operation) {
    final startTime = _startTimes.remove(operation);
    if (startTime == null) return null;

    final duration = DateTime.now().difference(startTime);
    _measurements.putIfAbsent(operation, () => []).add(duration);
    
    debugPrint('PerformanceMonitor: $operation took ${duration.inMilliseconds}ms');
    return duration;
  }

  /// Increment a counter
  void incrementCounter(String counter) {
    _counters[counter] = (_counters[counter] ?? 0) + 1;
  }

  /// Get counter value
  int getCounter(String counter) {
    return _counters[counter] ?? 0;
  }

  /// Get average duration for an operation
  Duration? getAverageDuration(String operation) {
    final measurements = _measurements[operation];
    if (measurements == null || measurements.isEmpty) return null;

    final totalMs = measurements.fold<int>(0, (sum, duration) => sum + duration.inMilliseconds);
    return Duration(milliseconds: (totalMs / measurements.length).round());
  }

  /// Get performance report
  Map<String, dynamic> getPerformanceReport() {
    final report = <String, dynamic>{};
    
    // Add timing data
    for (final operation in _measurements.keys) {
      final measurements = _measurements[operation]!;
      final avgDuration = getAverageDuration(operation);
      
      report[operation] = {
        'count': measurements.length,
        'average_ms': avgDuration?.inMilliseconds ?? 0,
        'min_ms': measurements.map((d) => d.inMilliseconds).reduce((a, b) => a < b ? a : b),
        'max_ms': measurements.map((d) => d.inMilliseconds).reduce((a, b) => a > b ? a : b),
      };
    }
    
    // Add counter data
    report['counters'] = Map.from(_counters);
    
    return report;
  }

  /// Print performance report
  void printReport() {
    final report = getPerformanceReport();
    debugPrint('=== PERFORMANCE REPORT ===');
    
    for (final operation in report.keys) {
      if (operation == 'counters') continue;
      
      final data = report[operation] as Map<String, dynamic>;
      debugPrint('$operation:');
      debugPrint('  Count: ${data['count']}');
      debugPrint('  Average: ${data['average_ms']}ms');
      debugPrint('  Min: ${data['min_ms']}ms');
      debugPrint('  Max: ${data['max_ms']}ms');
    }
    
    if (report['counters'] != null) {
      debugPrint('Counters:');
      final counters = report['counters'] as Map<String, dynamic>;
      for (final counter in counters.keys) {
        debugPrint('  $counter: ${counters[counter]}');
      }
    }
    
    debugPrint('=========================');
  }

  /// Clear all measurements
  void clear() {
    _startTimes.clear();
    _measurements.clear();
    _counters.clear();
  }

  /// Reset specific operation measurements
  void resetOperation(String operation) {
    _measurements.remove(operation);
    _startTimes.remove(operation);
  }
}

/// Mixin for adding performance monitoring to classes
mixin PerformanceMonitorMixin {
  final PerformanceMonitor _monitor = PerformanceMonitor();

  /// Start timing an operation
  void startPerformanceTimer(String operation) {
    _monitor.startTimer(operation);
  }

  /// End timing an operation
  Duration? endPerformanceTimer(String operation) {
    return _monitor.endTimer(operation);
  }

  /// Increment a performance counter
  void incrementPerformanceCounter(String counter) {
    _monitor.incrementCounter(counter);
  }

  /// Time a function execution
  Future<T> timeFunction<T>(String operation, Future<T> Function() function) async {
    startPerformanceTimer(operation);
    try {
      final result = await function();
      return result;
    } finally {
      endPerformanceTimer(operation);
    }
  }

  /// Time a synchronous function execution
  T timeFunctionSync<T>(String operation, T Function() function) {
    startPerformanceTimer(operation);
    try {
      final result = function();
      return result;
    } finally {
      endPerformanceTimer(operation);
    }
  }
}

/// Performance tracking constants
class PerformanceMetrics {
  static const String postLoad = 'post_load';
  static const String userDataLoad = 'user_data_load';
  static const String walletSync = 'wallet_sync';
  static const String transactionLoad = 'transaction_load';
  static const String profileStatsLoad = 'profile_stats_load';
  static const String cacheHit = 'cache_hit';
  static const String cacheMiss = 'cache_miss';
  static const String networkRequest = 'network_request';
  static const String uiRebuild = 'ui_rebuild';
  static const String likeAction = 'like_action';
  static const String postPagination = 'post_pagination';
}
