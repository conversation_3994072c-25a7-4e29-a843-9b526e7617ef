# 🚀 Complete Firebase Functions + Stripe Integration

## ✅ What's Already Set Up

### Firebase Functions Backend
- ✅ **Complete TypeScript Functions** in `functions/` directory
- ✅ **Stripe Payment Intent Creation** endpoint
- ✅ **Webhook Handler** for payment confirmations
- ✅ **Health Check** and configuration endpoints
- ✅ **Local Development** environment ready
- ✅ **CORS Support** for Flutter web/mobile

### Flutter App Integration
- ✅ **Payment Repository** updated for Firebase Functions
- ✅ **Stripe Payment Sheet** UI component
- ✅ **GetX Wallet Controller** integration
- ✅ **Test Mode** with simulation fallback
- ✅ **Error Handling** and user feedback

## 🔧 Quick Setup (5 Minutes)

### 1. Get Your Stripe Keys
1. Go to [Stripe Dashboard](https://dashboard.stripe.com/apikeys)
2. Copy your **Test** keys (start with `pk_test_` and `sk_test_`)

### 2. Configure Firebase Functions
```bash
# Replace with your actual Stripe test keys
firebase functions:config:set stripe.secret_key="sk_test_YOUR_SECRET_KEY"
firebase functions:config:set stripe.publishable_key="pk_test_YOUR_PUBLISHABLE_KEY"
firebase functions:config:set stripe.webhook_secret="whsec_temp_placeholder"
```

### 3. Update Local Config
Edit `functions/.runtimeconfig.json`:
```json
{
  "stripe": {
    "secret_key": "sk_test_YOUR_SECRET_KEY",
    "publishable_key": "pk_test_YOUR_PUBLISHABLE_KEY", 
    "webhook_secret": "whsec_temp_placeholder"
  }
}
```

### 4. Update Flutter App Keys
Edit `lib/main.dart` and `lib/repositories/payment_repository.dart`:
```dart
// Replace placeholder keys with your actual test keys
Stripe.publishableKey = kDebugMode 
  ? 'pk_test_YOUR_ACTUAL_TEST_KEY'
  : 'pk_live_YOUR_LIVE_KEY';
```

## 🧪 Testing

### Start Local Development
```bash
# Terminal 1: Start Firebase emulators
firebase emulators:start --only functions

# Terminal 2: Run Flutter app
flutter run -d chrome  # or your preferred device
```

### Test Payment Flow
1. **Open App** → Go to Wallet
2. **Tap "ReUp!"** → Select amount
3. **Payment Sheet Opens** → Shows Stripe integration
4. **Test Cards** (use these in mobile):
   - Success: `4242 4242 4242 4242`
   - Decline: `4000 0000 0000 0002`

### Verify Functions
```bash
# Health check
curl http://127.0.0.1:5001/money-mouthy/us-central1/healthCheck

# Test payment intent (with real keys)
curl -X POST http://127.0.0.1:5001/money-mouthy/us-central1/createPaymentIntent \
  -H "Content-Type: application/json" \
  -d '{"amount": 2500, "currency": "usd"}'
```

## 🚀 Deploy to Production

### 1. Deploy Functions
```bash
firebase deploy --only functions
```

### 2. Update Flutter for Production
The app automatically uses production endpoints when deployed.

### 3. Set Up Stripe Webhook
1. Go to [Stripe Webhooks](https://dashboard.stripe.com/webhooks)
2. Add endpoint: `https://us-central1-money-mouthy.cloudfunctions.net/stripeWebhook`
3. Select events: `payment_intent.succeeded`, `payment_intent.payment_failed`
4. Copy webhook secret and update config:
```bash
firebase functions:config:set stripe.webhook_secret="whsec_YOUR_WEBHOOK_SECRET"
```

## 📱 How It Works

### Payment Flow
```
Flutter App → Firebase Functions → Stripe API → Payment Processing → Webhook → Wallet Update
```

### Architecture
- **Frontend**: Flutter with GetX state management
- **Backend**: Firebase Functions (TypeScript)
- **Payments**: Stripe API integration
- **Database**: Firestore for wallet/transaction data

### Security
- ✅ **Client**: Only publishable keys
- ✅ **Server**: Secret keys in Firebase config
- ✅ **Validation**: Amount limits and input validation
- ✅ **Webhooks**: Signature verification

## 🔍 Troubleshooting

### Common Issues

**"Invalid API Key"**
- Update Stripe keys in Firebase config and local `.runtimeconfig.json`

**"CORS Error"**
- Functions include CORS headers, should work automatically

**"Payment Failed"**
- Check Stripe dashboard for detailed error messages
- Verify test card numbers

**"Function Not Found"**
- Ensure functions are deployed: `firebase deploy --only functions`
- Check function names match exactly

### Debug Commands
```bash
# View function logs
firebase functions:log

# Check current config
firebase functions:config:get

# Test local functions
curl http://127.0.0.1:5001/money-mouthy/us-central1/healthCheck
```

## 🎯 Production Checklist

- [ ] Replace test Stripe keys with live keys
- [ ] Deploy functions to production
- [ ] Set up production webhook endpoint
- [ ] Test with real payment methods
- [ ] Monitor function logs and errors
- [ ] Set up Stripe webhook monitoring

## 🔗 Endpoints

### Local Development
- Functions: `http://127.0.0.1:5001/money-mouthy/us-central1/`
- UI: `http://127.0.0.1:4000/`

### Production
- Functions: `https://us-central1-money-mouthy.cloudfunctions.net/`

### Available Functions
- `createPaymentIntent` - Create Stripe payment intent
- `stripeWebhook` - Handle Stripe webhook events
- `healthCheck` - Service health status
- `getStripeConfig` - Get client configuration

## 🎉 You're Ready!

Your Money Mouthy app now has:
- ✅ **Complete Stripe Integration**
- ✅ **Firebase Functions Backend** 
- ✅ **Secure Payment Processing**
- ✅ **Real-time Wallet Updates**
- ✅ **Production-Ready Architecture**

Just add your Stripe keys and you're live! 🚀
